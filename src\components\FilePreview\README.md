# FilePreview 文件预览组件

## 功能特性

- 支持多种文件格式预览：docx、xlsx、pdf、pptx、图片等
- H5环境下优先使用 @js-preview 组件进行预览
- 对于 pptx 和其他 Office 文件，使用 Office Online 内嵌预览
- 预览失败时自动回退到 web-view 预览
- 支持文件下载和点赞功能
- 移动端适配优化

## 支持的文件格式

### js-preview 支持的格式（H5环境）
- **docx**: 使用 @js-preview/docx
- **xlsx/xls**: 使用 @js-preview/excel  
- **pdf**: 使用 @js-preview/pdf

### Office Online 支持的格式（H5环境）
- **pptx**: PowerPoint 演示文稿
- **doc**: Word 文档（旧格式）
- **xls**: Excel 表格（旧格式）
- **ppt**: PowerPoint 演示文稿（旧格式）

### 原生支持的格式
- **图片**: jpg、jpeg、png、gif、webp、bmp

### 回退预览
- 其他格式文件使用 kkfileview 服务预览

## 使用方法

### 1. 基本使用

```vue
<template>
  <view>
    <file-preview ref="filePreviewRef" @clickDownload="handleDownload" @closeDialog="handleClose" />
  </view>
</template>

<script>
import FilePreview from '@/components/FilePreview/index.vue'

export default {
  components: {
    FilePreview
  },
  methods: {
    // 预览文件
    previewFile() {
      // 方式1: 传入文件URL字符串
      this.$refs.filePreviewRef.fileLoad('https://example.com/file.docx')
      
      // 方式2: 传入完整的文件对象
      const fileInfo = {
        manageAddress: 'https://example.com/file.docx',
        manageId: '123',
        isDownload: '1',
        likeFlag: false
      }
      this.$refs.filePreviewRef.fileLoad(fileInfo)
    },
    
    handleDownload(fileId) {
      console.log('下载文件:', fileId)
    },
    
    handleClose() {
      console.log('关闭预览')
    }
  }
}
</script>
```

### 2. 文件对象参数说明

```javascript
const fileInfo = {
  manageAddress: 'https://example.com/file.docx', // 文件URL（必需）
  manageId: '123',                                // 文件ID（用于点赞和下载统计）
  isDownload: '1',                               // 是否允许下载 '1'-允许 '0'-不允许
  likeFlag: false,                               // 是否已点赞
  url: 'https://example.com/file.docx'           // 备用URL字段
}
```

### 3. 事件说明

- `@clickDownload`: 点击下载按钮时触发，参数为文件ID
- `@closeDialog`: 关闭预览时触发

## 环境差异

### H5环境
- 优先使用 @js-preview 组件进行预览
- 支持 Office Online 内嵌预览
- 预览失败时回退到 web-view

### 非H5环境（App、小程序）
- 直接使用 web-view 进行预览
- 通过 kkfileview 服务处理文件预览

## 依赖说明

### npm 依赖
```json
{
  "@js-preview/docx": "^1.6.4",
  "@js-preview/excel": "^1.7.14", 
  "@js-preview/pdf": "^2.0.10",
  "js-base64": "^3.7.7"
}
```

### 环境变量
```
VUE_APP_KK_URL=https://your-kkfileview-server.com
```

## 注意事项

1. **跨域问题**: Office Online 预览需要文件URL支持跨域访问
2. **文件大小**: 建议预览文件大小不超过50MB
3. **网络环境**: js-preview 需要良好的网络环境加载文件
4. **浏览器兼容**: 某些预览功能可能在旧版浏览器中不支持

## 故障排除

### 预览失败
1. 检查文件URL是否可访问
2. 检查网络连接
3. 查看控制台错误信息
4. 确认文件格式是否支持

### 样式问题
1. 确保引入了相关CSS文件
2. 检查容器高度设置
3. 查看移动端适配样式
