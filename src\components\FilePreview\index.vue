<!--
 * @Description: 文件预览
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @Date: 2023-05-10 13:14:50
 * @LastEditTime: 2025-07-23 09:21:21
-->
<template>
  <view class="file-preview-container">
    <!-- 标题栏 -->
    <view class="header">
      <view class="title">{{ title }}</view>
      <view class="actions">
        <view v-if="showDownloadBtn" @click="download" class="action-btn download-btn">
          <text class="iconfont icon-xiazai"></text>
        </view>
        <view v-if="showLikeBtn" @click="support" class="action-btn like-btn" :class="likeFlag ? 'active-collect' : ''">
          <text class="iconfont icon-xihuan"></text>
        </view>
      </view>
    </view>

    <!-- 文件预览内容 -->
    <view v-if="downloadUrl" class="preview-content">
      <!-- 如果js-preview渲染失败，使用web-view预览 -->
      <web-view
        v-if="useWebViewPreview"
        :src="webViewUrl"
        class="web-view-container"
      />

      <!-- 优先使用@js-preview组件 -->
      <template v-else>
        <!-- docx文件预览容器 -->
        <view
          v-if="isDocxFile(downloadUrl)"
          ref="docxContainer"
          class="js-preview-container"
          id="docxContainer"
        ></view>

        <!-- excel文件预览容器 -->
        <view
          v-else-if="isExcelFile(downloadUrl)"
          ref="excelContainer"
          class="js-preview-container"
          id="excelContainer"
        ></view>

        <!-- pdf文件预览容器 -->
        <view
          v-else-if="isPdfFile(downloadUrl)"
          ref="pdfContainer"
          class="js-preview-container"
          id="pdfContainer"
        ></view>

        <!-- pptx文件预览容器 -->
        <view
          v-else-if="isPptxFile(downloadUrl)"
          ref="pptxContainer"
          class="js-preview-container pptx-container"
          id="pptxContainer"
        ></view>

        <!-- 图片预览 -->
        <view v-else-if="isImgPath(downloadUrl)" class="image-preview">
          <image :src="downloadUrl" mode="aspectFit" class="preview-image" />
        </view>

        <!-- 其他Office文件使用Office Online预览 -->
        <view
          v-else-if="isOfficeFile(downloadUrl)"
          ref="officeContainer"
          class="js-preview-container office-container"
          id="officeContainer"
        ></view>

        <!-- 其他文件使用web-view预览 -->
        <web-view
          v-else
          :src="webViewUrl"
          class="web-view-container"
        />
      </template>
    </view>
  </view>
</template>

<script>
  import { Base64 } from "js-base64"
  import { isDocxFile, isExcelFile, isPdfFile, isPptxFile, isOfficeFile, isImgPath } from "@/utils/fileType"
  import { toast } from "@/utils/common"
  import { editRelateNumber } from "@/api/course/manage"

  // #ifdef H5
  import jsPreviewDocx from '@js-preview/docx'
  import jsPreviewExcel from '@js-preview/excel'
  import jsPreviewPdf from '@js-preview/pdf'

  import '@js-preview/docx/lib/index.css'
  import '@js-preview/excel/lib/index.css'
  // #endif

  export default {
    name: "FilePreview",
    data() {
      return {
        title: "文件预览",
        downloadUrl: "",
        webViewUrl: "",
        likeFlag: false,
        meansItem: null,
        primaryKeyId: "",
        useWebViewPreview: false,
        kkFileURL: process.env.VUE_APP_KK_URL,

        // 预览器实例
        docxPreviewer: null,
        excelPreviewer: null,
        pdfPreviewer: null
      }
    },

    computed: {
      // 是否显示下载按钮
      showDownloadBtn() {
        return this.downloadUrl && (this.meansItem?.isDownload === '1' || !this.meansItem)
      },

      // 是否显示点赞按钮
      showLikeBtn() {
        return this.primaryKeyId && this.meansItem
      }
    },

    methods: {
      // 文件类型判断方法
      isDocxFile,
      isExcelFile,
      isPdfFile,
      isPptxFile,
      isOfficeFile,
      isImgPath,

      // 文件加载方法
      fileLoad(item) {
        // 兼容不同的调用方式
        if (typeof item === 'string') {
          // 如果传入的是字符串，说明是文件URL
          this.downloadUrl = item
          this.meansItem = null
          this.likeFlag = false
          this.primaryKeyId = ""
        } else if (item && typeof item === 'object') {
          // 如果传入的是对象，包含完整的文件信息
          this.meansItem = item
          this.likeFlag = item.likeFlag || false
          this.downloadUrl = item.manageAddress || item.url || item

          if (item.manageId) {
            this.primaryKeyId = item.manageId
          }
        }

        // 设置web-view预览URL
        this.webViewUrl = `${this.kkFileURL}/onlinePreview?url=${encodeURIComponent(
          Base64.encode(this.downloadUrl)
        )}`

        // #ifdef H5
        // H5环境下尝试使用js-preview
        this.$nextTick(() => {
          this.initPreviewer(this.downloadUrl)
        })
        // #endif

        // #ifndef H5
        // 非H5环境直接使用web-view
        this.useWebViewPreview = true
        // #endif
      },

      // #ifdef H5
      // 初始化预览器（仅H5环境）
      async initPreviewer(fileUrl) {
        try {
          if (this.isDocxFile(fileUrl)) {
            await this.initDocxPreviewer(fileUrl)
          } else if (this.isExcelFile(fileUrl)) {
            await this.initExcelPreviewer(fileUrl)
          } else if (this.isPdfFile(fileUrl)) {
            await this.initPdfPreviewer(fileUrl)
          } else if (this.isPptxFile(fileUrl)) {
            await this.initPptxPreviewer(fileUrl)
          } else if (this.isOfficeFile(fileUrl)) {
            await this.initOfficePreviewer(fileUrl)
          }
        } catch (error) {
          console.error('预览失败:', error)
          this.handlePreviewError()
        }
      },

      // 初始化docx预览器
      async initDocxPreviewer(fileUrl) {
        const container = document.getElementById('docxContainer')
        if (!container) return

        if (this.docxPreviewer) {
          this.docxPreviewer.destroy?.()
        }

        this.docxPreviewer = jsPreviewDocx.init(container)
        await this.docxPreviewer.preview(fileUrl)
        console.log('docx预览完成')
      },

      // 初始化excel预览器
      async initExcelPreviewer(fileUrl) {
        const container = document.getElementById('excelContainer')
        if (!container) return

        if (this.excelPreviewer) {
          this.excelPreviewer.destroy?.()
        }

        this.excelPreviewer = jsPreviewExcel.init(container)
        await this.excelPreviewer.preview(fileUrl)
        console.log('excel预览完成')
      },

      // 初始化pdf预览器
      async initPdfPreviewer(fileUrl) {
        const container = document.getElementById('pdfContainer')
        if (!container) return

        if (this.pdfPreviewer) {
          this.pdfPreviewer.destroy?.()
        }

        this.pdfPreviewer = jsPreviewPdf.init(container, {
          onError: (e) => {
            console.log('pdf预览发生错误', e)
            this.handlePreviewError()
          },
          onRendered: () => {
            console.log('pdf渲染完成')
          }
        })
        await this.pdfPreviewer.preview(fileUrl)
        console.log('pdf预览完成')
      },

      // 初始化pptx预览器
      async initPptxPreviewer(fileUrl) {
        const container = document.getElementById('pptxContainer')
        if (!container) return

        container.innerHTML = ''

        try {
          const officeOnlineUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`

          const pptxIframeHtml = `
            <iframe
              src="${officeOnlineUrl}"
              width="100%"
              height="100%"
              frameborder="0"
              style="border: none; width: 100%; height: 100%;"
              allowfullscreen>
            </iframe>
          `

          container.innerHTML = pptxIframeHtml
          console.log('pptx预览完成 - 使用Office Online')
        } catch (error) {
          console.error('PPTX Office Online预览失败:', error)
          this.handlePreviewError()
        }
      },

      // 初始化其他Office文件预览器
      async initOfficePreviewer(fileUrl) {
        const container = document.getElementById('officeContainer')
        if (!container) return

        container.innerHTML = ''

        try {
          const officeOnlineUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`

          const officeIframeHtml = `
            <iframe
              src="${officeOnlineUrl}"
              width="100%"
              height="100%"
              frameborder="0"
              style="border: none; width: 100%; height: 100%;"
              allowfullscreen>
            </iframe>
          `

          container.innerHTML = officeIframeHtml
          console.log('其他Office文件预览完成 - 使用Office Online')
        } catch (error) {
          console.error('其他Office文件预览失败:', error)
          this.handlePreviewError()
        }
      },
      // #endif

      // 处理预览错误
      handlePreviewError() {
        console.log("文档渲染失败，回退到web-view预览")
        toast("使用备用预览方式")
        this.useWebViewPreview = true
      },

      // 下载文件
      download() {
        // #ifdef H5
        window.open(this.downloadUrl, "_blank")
        // #endif

        // #ifndef H5
        uni.downloadFile({
          url: this.downloadUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              toast("下载成功")
            }
          },
          fail: () => {
            toast("下载失败")
          }
        })
        // #endif

        this.$emit("clickDownload", this.primaryKeyId)
      },

      // 点赞/取消点赞
      async support() {
        if (!this.primaryKeyId) {
          toast("无法获取文件信息")
          return
        }

        const originalLikeFlag = this.likeFlag
        this.likeFlag = !this.likeFlag

        try {
          await editRelateNumber({
            manageId: this.primaryKeyId,
            likeNumber: this.likeFlag ? 1 : -1
          })

          if (this.likeFlag) {
            toast("点赞成功！")
          } else {
            toast("取消点赞成功！")
          }
        } catch (error) {
          console.error('点赞操作失败:', error)
          this.likeFlag = originalLikeFlag // 回滚状态
          toast("操作失败，请重试")
        }
      },

      // 清理预览器
      cleanupPreviewers() {
        // #ifdef H5
        if (this.docxPreviewer) {
          this.docxPreviewer.destroy?.()
          this.docxPreviewer = null
        }
        if (this.excelPreviewer) {
          this.excelPreviewer.destroy?.()
          this.excelPreviewer = null
        }
        if (this.pdfPreviewer) {
          this.pdfPreviewer.destroy?.()
          this.pdfPreviewer = null
        }

        // 清理容器内容
        const containers = ['pptxContainer', 'officeContainer']
        containers.forEach(id => {
          const container = document.getElementById(id)
          if (container) {
            container.innerHTML = ''
          }
        })
        // #endif
      },

      // 关闭预览
      close() {
        this.cleanupPreviewers()
        this.downloadUrl = ""
        this.webViewUrl = ""
        this.useWebViewPreview = false
        this.$emit("closeDialog")
      }
    },

    beforeDestroy() {
      this.cleanupPreviewers()
    }
  }
</script>

<style scoped lang="scss">
  .file-preview-container {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #fff;
  }

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    background-color: #f8f9fa;
    border-bottom: 1rpx solid #e9ecef;

    .title {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;
    }

    .actions {
      display: flex;
      align-items: center;
      gap: 40rpx;

      .action-btn {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        background-color: #fff;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        .iconfont {
          font-size: 32rpx;
        }
      }

      .download-btn .iconfont {
        color: #2695f9;
      }

      .like-btn .iconfont {
        color: #9c9c9c;
        transition: color 0.3s;
      }

      .like-btn.active-collect .iconfont {
        color: #dc4853;
      }
    }
  }

  .preview-content {
    flex: 1;
    width: 100%;
    overflow: hidden;
  }

  .web-view-container {
    width: 100%;
    height: 100%;
  }

  .js-preview-container {
    width: 100%;
    height: 100%;
    overflow: auto;
  }

  .pptx-container,
  .office-container {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  .image-preview {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;

    .preview-image {
      max-width: 100%;
      max-height: 100%;
    }
  }
</style>
